const express = require('express')
const { isAuthenticated, authorizationRoles } = require('../middlewares/auth')
const {createJob, allJobs, oneJob, saveJob, getSavedJobs} = require('../controllers/JobControllers')
const {job<PERSON><PERSON>da<PERSON>,validate<PERSON><PERSON><PERSON>,JobIdValidator } = require('../middlewares/validators');
const router = express.Router()


router.route("/create/job").post(isAuthenticated, authorizationRoles("admin") ,jobValidator(),validateHandler, createJob)

router.route("/jobs").get(allJobs) ;

router.route("/job/:id").get(JobIdValidator(),validate<PERSON>and<PERSON>,oneJob) ;

router.route("/saveJob/:id").get(isAuthenticated,JobIdValidator(),validateHandler, saveJob) ;

router.route("/getSavedJobs").get(isAuthenticated, getSavedJobs) ;

module.exports = router ;