# JobLane : Job application portal project

### Installation of JobLane.

#### Setting up MERN Stack:

Follow the installation instructions for MongoDB, Express.js, React.js, and Node.js.

<h2>🛠️ Installation Steps:</h2>

1. Clone the repository to your local machine:
    ```bash
    git clone https://github.com/sujaltangde/JobLane.git 
    ```

2. Change into the frontend directory:
    ```bash
    cd client
    ```

3. Install the frontend dependencies:
    ```bash
    npm install
    ```

4. Run the development frontend server:
    ```bash
    npm run dev
    ```

5. Return to the main directory and change into the backend directory:
    ```bash
    cd ..
    cd server
    ```

6. Install the backend dependencies:
    ```bash
    npm install
    ```
7. Configure env file (generate config.env template by running the following command)
    ```bash
    cp config.env config.sample.env
    ```
8. Open config.env file from config folder replace the following credentials
    ```bash
    DB = 
    CLOUDINARY_NAME = 
    CLOUDINARY_API_KEY =
    CLOUDINARY_API_SECRET =
    ```
9. Run the development backend server:
    ```bash
    nodemon server.js
    ```



### Learning Objectives

- Mastering MERN stack development for building modern web applications.
- Understanding comman features of job portal websites and there workflow.
- Gaining practical experience in software development, testing, and deployment in a real-world project scenario.
