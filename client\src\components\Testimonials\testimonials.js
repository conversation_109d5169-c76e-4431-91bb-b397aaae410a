const testimonials = [
    {
        name: "<PERSON>",
        position: "Software Engineer",
        company: "TechCorp",
        image: "https://dpemoji.com/wp-content/uploads/2023/01/Whatsapp-dp-for-boys-57.png",
        text: "<PERSON><PERSON><PERSON> helped me find my dream job quickly and easily. The platform is user-friendly and has a great selection of job listings."
    },
    {
        name: "<PERSON>",
        position: "Product Manager",
        company: "InnovateX",
        image: "https://t3.ftcdn.net/jpg/06/36/69/86/360_F_636698674_DroChEj5eWmZiaZOSDMnj8hcDqqw74Fp.jpg",
        text: "I love how JobLane connects job seekers with top companies. It made my job search stress-free and successful!"
    },
    {
        name: "<PERSON>",
        position: "Data Analyst",
        company: "TechNet",
        image: "https://dpemoji.com/wp-content/uploads/2023/01/Whatsapp-dp-for-boys-57.png",
        text: "<PERSON><PERSON><PERSON> provided me with valuable insights into job trends and opportunities in my field. Highly recommend!"
    },
    {
        name: "<PERSON>",
        position: "UX Designer",
        company: "CreativeMind",
        image: "https://t3.ftcdn.net/jpg/06/36/69/86/360_F_636698674_DroChEj5eWmZiaZOSDMnj8hcDqqw74Fp.jpg",
        text: "JobLane's intuitive interface and personalized job recommendations made my job search efficient and enjoyable."
    },
    {
        name: "<PERSON> <PERSON>",
        position: "Marketing Specialist",
        company: "DigitalEdge",
        image: "https://dpemoji.com/wp-content/uploads/2023/01/Whatsapp-dp-for-boys-57.png",
        text: "JobLane connected me with top-tier companies and helped me land my dream job. Thank you for the amazing platform!"
    },
];

export default testimonials;
