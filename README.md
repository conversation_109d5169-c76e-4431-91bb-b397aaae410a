<h1 align="center" id="title">JobLane</h1>

<p align="center"><img src="https://socialify.git.ci/sujaltangde/JobLane/image?forks=1&issues=1&language=1&name=1&owner=1&pulls=1&stargazers=1&theme=Dark" alt="JobLane" width="640" height="320" /></p>

[![Hits](https://hits.sh/github.com/sujaltangde/JobLane.svg?color=116acc)](https://hits.sh/github.com/sujaltangde/JobLane/)

<p id="description">Full Stack Job application portal built using MERN stack, Focusing on simplifying the job applications.</p>

<h2>🚀 Demo</h2>

[Website](https://joblane.vercel.app/)<br />

  
  
<h2>🧐 Features</h2>

Here're some of the project's best features:

*   User Authentication and Authorization
*   Job Search and Filtering
*   Save and Track Jobs
*   Resume Management
*   Profile Management
*   Admin Dashboard
*   Job Posting
*   Application Tracking
*   Messaging and Notifications
*   Responsive Design

<h2>🔥 Getting Started</h2>

To get started with the assignment project, follow these steps:

### Prerequisites

- **Node.js**: Make sure you have Node.js installed on your computer. You can download it from [nodejs.org](https://nodejs.org).

### Installation

1. Clone the repository to your local machine:
    ```bash
    git clone https://github.com/sujaltangde/JobLane.git 
    ```

2. Change into the frontend directory:
    ```bash
    cd client
    ```

3. Install the frontend dependencies:
    ```bash
    npm install
    ```

4. Run the development server:
    ```bash
    npm run dev
    ```

5. Return to the main directory and change into the backend directory:
    ```bash
    cd ..
    cd server
    ```

6. Install the backend dependencies:
    ```bash
    npm install
    ```

7. Run the development server:
    ```bash
    nodemon server.js
    ```



<h2>🍰 Contribution Guidelines:</h2>

Any contributor who wishes to contribute to this project should first read the README thoroughly. Study how the project is built and done, familiarize yourself with its structure and components. Take note of any bugs present in the project. If you find any raise an issue on the project's repository. Wait until a mentor assigns the issue to you. Once assigned start working on the development of the fix or improvement. After completing the development raise a pull request (PR) for the changes to be reviewed and merged into the project.

  
  
<h2>💻 Built with</h2>

- **Frontend**: React.js, Redux
- **Backend**: Node.js, Express.js
- **Database**: MongoDB 
- **Styling**: Tailwind CSS, Material UI, Mantine UI
