{"name": "server", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cloudinary": "^1.40.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.0", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.1", "mongoose": "^7.5.0", "nodemon": "^3.1.0", "validator": "^13.11.0"}}