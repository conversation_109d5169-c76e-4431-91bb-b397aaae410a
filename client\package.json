{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/serialize": "^1.1.4", "@mantine/carousel": "^6.0.19", "@mantine/core": "^6.0.19", "@mantine/hooks": "^6.0.19", "@mui/icons-material": "^5.14.8", "@mui/material": "^5.14.8", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.5.0", "chart.js": "^4.4.0", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-countup": "^6.4.2", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-icons": "^4.10.1", "react-multi-carousel": "^2.8.4", "react-paginate": "^8.2.0", "react-redux": "^8.1.2", "react-router": "^6.15.0", "react-router-dom": "^6.15.0", "react-slick": "^0.30.2", "react-toastify": "^9.1.3", "slick-carousel": "^1.8.1", "typewriter-effect": "^2.21.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}